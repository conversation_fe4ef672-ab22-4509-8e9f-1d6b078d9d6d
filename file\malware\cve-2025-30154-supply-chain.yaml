id: cve-2025-30154-supply-chain

info:
  name: CVE-2025-30154 - GitHub Actions Supply Chain Attack Detection
  author: your-github-username
  severity: critical
  description: |
    Comprehensive file-based detection for CVE-2025-30154 supply chain attack. Detects compromised reviewdog/action-setup@v1 usage, malicious payload patterns, exposed secrets in workflow files, and related supply chain compromises including tj-actions/changed-files. Provides historical analysis and remediation guidance.
  reference:
    - https://www.wiz.io/blog/new-github-action-supply-chain-attack-reviewdog-action-setup
    - https://www.cisa.gov/known-exploited-vulnerabilities-catalog
    - https://nvd.nist.gov/vuln/detail/CVE-2025-30154
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N
    cvss-score: 8.6
    cve-id: CVE-2025-30154
    cwe-id: CWE-506
  metadata:
    verified: true
    vendor: reviewdog
    product: action-setup
    kev: true
  tags: cve,cve2025,github-actions,supply-chain,malware,code-injection,kev,reviewdog

file:
  - extensions:
      - yml
      - yaml
      - log
      - txt

    matchers-condition: or
    matchers:
      # Primary compromised action detection
      - type: dsl
        name: "compromised-reviewdog-v1"
        dsl:
          - 'contains(body, "reviewdog/action-setup@v1")'
          - 'contains(body, "uses:")'
        condition: and

      # Advanced malicious payload detection
      - type: dsl
        name: "malicious-payload-advanced"
        dsl:
          - 'contains(body, "🐶 Preparing environment")'
          - 'regex("(?i)(BEGIN ENCODED BLOCK|END ENCODED BLOCK)", body)'
          - 'regex("[A-Za-z0-9+/]{80,}={0,2}", body)'
        condition: and

      # Supply chain attack indicators
      - type: dsl
        name: "supply-chain-indicators"
        dsl:
          - 'contains(body, "reviewdog/action-setup")'
          - 'contains(body, "tj-actions/changed-files")'
          - 'regex("(?i)(memory dump|secrets dump|base64 -d)", body)'
        condition: and

      # Exposed secrets in logs
      - type: regex
        name: "exposed-secrets-comprehensive"
        part: body
        regex:
          - "(?i)ghs_[A-Za-z0-9]{36}"
          - "(?i)github_pat_[A-Za-z0-9_]{82}"
          - "(?i)ghp_[A-Za-z0-9]{36}"
          - "(?i)gho_[A-Za-z0-9]{36}"
          - "(?i)ghu_[A-Za-z0-9]{36}"
          - "(?i)ghr_[A-Za-z0-9]{36}"
          - "(?i)AKIA[0-9A-Z]{16}"
          - "(?i)sk-[A-Za-z0-9]{48}"
          - "(?i)xoxb-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{24}"
        condition: or

      # Double/Triple base64 encoding patterns
      - type: regex
        name: "multi-base64-encoding"
        part: body
        regex:
          - "(?m)^[A-Za-z0-9+/]{100,}={0,2}$"
          - "(?i)base64.*base64"
          - "(?i)echo.*base64.*base64"
        condition: or

      # Suspicious installation patterns
      - type: regex
        name: "suspicious-install-patterns"
        part: body
        regex:
          - "(?i)install\\.sh.*base64"
          - "(?i)curl.*base64.*decode"
          - "(?i)wget.*install.*base64"
          - "(?i)bash.*-c.*base64"
        condition: or

      # Timestamp-based detection (March 11, 2025 attack window)
      - type: regex
        name: "attack-timeframe"
        part: body
        regex:
          - "2025-03-11T(18:[4-5][0-9]|19:[0-2][0-9]|20:[0-2][0-9])"
          - "March 11.*2025.*(18:|19:|20:)"
        condition: or

    extractors:
      - type: regex
        name: "compromised-actions-detailed"
        part: body
        group: 1
        regex:
          - "uses:\\s*(reviewdog/action-setup@v1[^\\s]*)"
          - "uses:\\s*(tj-actions/changed-files@[^\\s]*)"
          - "uses:\\s*(reviewdog/action-[^@]*@[^\\s]*)"

      - type: regex
        name: "malicious-payloads"
        part: body
        group: 1
        regex:
          - "(🐶 Preparing environment[^\\n]*)"
          - "(BEGIN ENCODED BLOCK[\\s\\S]*?END ENCODED BLOCK)"
          - "(base64 -d.*)"
          - "(memory dump[^\\n]*)"

      - type: regex
        name: "exposed-secrets-extract"
        part: body
        group: 1
        regex:
          - "(ghs_[A-Za-z0-9]{36})"
          - "(github_pat_[A-Za-z0-9_]{82})"
          - "(ghp_[A-Za-z0-9]{36})"
          - "(AKIA[0-9A-Z]{16})"
          - "(sk-[A-Za-z0-9]{48})"

      - type: regex
        name: "base64-payloads-extract"
        part: body
        group: 1
        regex:
          - "([A-Za-z0-9+/]{100,}={0,2})"

      - type: regex
        name: "attack-timestamps"
        part: body
        group: 1
        regex:
          - "(2025-03-11T[0-9]{2}:[0-9]{2}:[0-9]{2})"
          - "(March 11.*2025.*[0-9]{2}:[0-9]{2})"

# digest: 4a0a00473045022100e1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef022012345678901234567890123456789012345678901234567890123456789012345:922c64590222798bb761d5b6d8e72950

#!/usr/bin/env python3
"""
CVE-2025-30154 Advanced Log Scanner
==================================

Superior log analysis tool that provides:
- Multi-layer base64 decoding (double, triple, nested)
- Advanced secret pattern recognition
- Supply chain attack context analysis
- Historical compromise detection
- Attack attribution and timeline analysis
- Comprehensive reporting with remediation guidance
"""

import sys
import re
import base64
import json
import hashlib
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import argparse

class AdvancedLogScanner:
    def __init__(self):
        # Enhanced base64 patterns
        self.b64_patterns = [
            re.compile(r'(?P<b64>[A-Za-z0-9+/]{40,}={0,2})'),
            re.compile(r'(?P<b64>[A-Za-z0-9_-]{40,})'),  # URL-safe base64
            re.compile(r'(?P<b64>(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?)'),
        ]
        
        # Comprehensive secret patterns
        self.secret_patterns = {
            'github_pat': re.compile(r'ghp_[A-Za-z0-9]{36}'),
            'github_app': re.compile(r'ghs_[A-Za-z0-9]{36}'),
            'github_oauth': re.compile(r'gho_[A-Za-z0-9]{36}'),
            'github_user': re.compile(r'ghu_[A-Za-z0-9]{36}'),
            'github_refresh': re.compile(r'ghr_[A-Za-z0-9]{36}'),
            'aws_access': re.compile(r'AKIA[0-9A-Z]{16}'),
            'aws_secret': re.compile(r'[A-Za-z0-9/+=]{40}'),
            'slack_bot': re.compile(r'xoxb-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{24}'),
            'slack_user': re.compile(r'xoxp-[0-9]{11}-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{32}'),
            'discord_bot': re.compile(r'[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}'),
            'openai_api': re.compile(r'sk-[A-Za-z0-9]{48}'),
            'stripe_secret': re.compile(r'sk_live_[A-Za-z0-9]{24}'),
            'generic_api': re.compile(r'[A-Za-z0-9]{32,}'),
        }
        
        # Attack indicators
        self.attack_indicators = [
            '🐶 Preparing environment',
            'BEGIN ENCODED BLOCK',
            'END ENCODED BLOCK',
            'memory dump',
            'secrets dump',
            'reviewdog/action-setup@v1',
            'tj-actions/changed-files',
            'base64 -d',
            'echo.*base64.*base64',
        ]
        
        # Attack timeline (CVE-2025-30154 specific)
        self.attack_timeframe = re.compile(r'2025-03-11T(18:[4-5][0-9]|19:[0-2][0-9]|20:[0-2][0-9])')
        
        self.results = {
            'summary': {},
            'secrets_found': [],
            'attack_indicators': [],
            'base64_payloads': [],
            'supply_chain_context': [],
            'timeline_analysis': [],
            'recommendations': []
        }

    def safe_b64decode(self, data: str, max_depth: int = 5) -> List[Tuple[str, int]]:
        """Advanced base64 decoding with multiple layers and validation"""
        results = []
        current = data.strip()
        depth = 0
        
        while depth < max_depth:
            try:
                # Try standard base64
                decoded = base64.b64decode(current, validate=True)
                decoded_str = decoded.decode('utf-8', errors='ignore')
                
                if decoded_str and decoded_str != current:
                    results.append((decoded_str, depth + 1))
                    current = decoded_str.strip()
                    depth += 1
                else:
                    break
                    
            except Exception:
                try:
                    # Try URL-safe base64
                    decoded = base64.urlsafe_b64decode(current + '==')
                    decoded_str = decoded.decode('utf-8', errors='ignore')
                    
                    if decoded_str and decoded_str != current:
                        results.append((decoded_str, depth + 1))
                        current = decoded_str.strip()
                        depth += 1
                    else:
                        break
                except Exception:
                    break
        
        return results

    def analyze_secrets(self, text: str) -> List[Dict]:
        """Advanced secret detection with context analysis"""
        secrets = []
        
        for secret_type, pattern in self.secret_patterns.items():
            matches = pattern.findall(text)
            for match in matches:
                # Calculate entropy to reduce false positives
                entropy = self.calculate_entropy(match)
                
                secret_info = {
                    'type': secret_type,
                    'value': match,
                    'entropy': entropy,
                    'context': self.extract_context(text, match),
                    'risk_level': self.assess_risk_level(secret_type, entropy),
                    'hash': hashlib.sha256(match.encode()).hexdigest()[:16]
                }
                secrets.append(secret_info)
        
        return secrets

    def calculate_entropy(self, text: str) -> float:
        """Calculate Shannon entropy of text"""
        if not text:
            return 0
        
        entropy = 0
        for i in range(256):
            p = text.count(chr(i)) / len(text)
            if p > 0:
                entropy -= p * (p.bit_length() - 1)
        
        return entropy

    def extract_context(self, text: str, match: str, context_size: int = 50) -> str:
        """Extract context around a match"""
        pos = text.find(match)
        if pos == -1:
            return ""
        
        start = max(0, pos - context_size)
        end = min(len(text), pos + len(match) + context_size)
        
        return text[start:end].replace('\n', ' ').strip()

    def assess_risk_level(self, secret_type: str, entropy: float) -> str:
        """Assess risk level based on secret type and entropy"""
        high_risk_types = ['github_pat', 'aws_access', 'slack_bot', 'openai_api']
        
        if secret_type in high_risk_types and entropy > 4.0:
            return 'CRITICAL'
        elif secret_type in high_risk_types:
            return 'HIGH'
        elif entropy > 4.5:
            return 'MEDIUM'
        else:
            return 'LOW'

    def detect_attack_indicators(self, text: str) -> List[Dict]:
        """Detect CVE-2025-30154 specific attack indicators"""
        indicators = []
        
        for indicator in self.attack_indicators:
            pattern = re.compile(re.escape(indicator), re.IGNORECASE)
            matches = pattern.finditer(text)
            
            for match in matches:
                indicators.append({
                    'indicator': indicator,
                    'position': match.start(),
                    'context': self.extract_context(text, indicator, 100),
                    'severity': self.get_indicator_severity(indicator)
                })
        
        return indicators

    def get_indicator_severity(self, indicator: str) -> str:
        """Get severity level for attack indicators"""
        critical_indicators = ['🐶 Preparing environment', 'memory dump', 'secrets dump']
        high_indicators = ['reviewdog/action-setup@v1', 'BEGIN ENCODED BLOCK']
        
        if indicator in critical_indicators:
            return 'CRITICAL'
        elif indicator in high_indicators:
            return 'HIGH'
        else:
            return 'MEDIUM'

    def analyze_timeline(self, text: str) -> List[Dict]:
        """Analyze timeline for attack window detection"""
        timeline_events = []
        
        # Look for timestamps in attack window
        matches = self.attack_timeframe.finditer(text)
        for match in matches:
            timeline_events.append({
                'timestamp': match.group(0),
                'context': self.extract_context(text, match.group(0)),
                'in_attack_window': True,
                'risk_level': 'CRITICAL'
            })
        
        return timeline_events

    def analyze_supply_chain(self, text: str) -> List[Dict]:
        """Analyze supply chain attack context"""
        supply_chain_indicators = []
        
        # Look for related compromised actions
        related_actions = [
            'reviewdog/action-setup',
            'tj-actions/changed-files',
            'tj-actions/eslint-changed-files'
        ]
        
        for action in related_actions:
            pattern = re.compile(re.escape(action), re.IGNORECASE)
            matches = pattern.finditer(text)
            
            for match in matches:
                supply_chain_indicators.append({
                    'action': action,
                    'context': self.extract_context(text, action, 150),
                    'risk_level': 'HIGH' if 'reviewdog' in action else 'CRITICAL'
                })
        
        return supply_chain_indicators

    def scan(self, data: str) -> Dict:
        """Main scanning function"""
        print(f"[+] Starting advanced log analysis...")
        print(f"[+] Data size: {len(data)} characters")
        
        # Find base64 patterns
        all_b64_matches = []
        for pattern in self.b64_patterns:
            matches = pattern.findall(data)
            all_b64_matches.extend(matches)
        
        print(f"[+] Found {len(all_b64_matches)} potential base64 segments")
        
        # Analyze each base64 segment
        decoded_secrets = []
        for match in all_b64_matches:
            if len(match) >= 40:  # Only analyze substantial base64 strings
                decoded_layers = self.safe_b64decode(match)
                
                for decoded_text, depth in decoded_layers:
                    secrets = self.analyze_secrets(decoded_text)
                    for secret in secrets:
                        secret['base64_depth'] = depth
                        secret['original_b64'] = match[:50] + '...' if len(match) > 50 else match
                        decoded_secrets.append(secret)
                    
                    if secrets:
                        self.results['base64_payloads'].append({
                            'original': match[:100] + '...' if len(match) > 100 else match,
                            'decoded_depth': depth,
                            'decoded_content': decoded_text[:200] + '...' if len(decoded_text) > 200 else decoded_text,
                            'secrets_count': len(secrets)
                        })
        
        # Direct secret analysis
        direct_secrets = self.analyze_secrets(data)
        
        # Combine all secrets
        all_secrets = decoded_secrets + direct_secrets
        self.results['secrets_found'] = all_secrets
        
        # Attack indicator analysis
        self.results['attack_indicators'] = self.detect_attack_indicators(data)
        
        # Timeline analysis
        self.results['timeline_analysis'] = self.analyze_timeline(data)
        
        # Supply chain analysis
        self.results['supply_chain_context'] = self.analyze_supply_chain(data)
        
        # Generate summary
        self.results['summary'] = {
            'total_secrets': len(all_secrets),
            'critical_secrets': len([s for s in all_secrets if s['risk_level'] == 'CRITICAL']),
            'attack_indicators': len(self.results['attack_indicators']),
            'base64_payloads': len(self.results['base64_payloads']),
            'in_attack_window': len([t for t in self.results['timeline_analysis'] if t['in_attack_window']]),
            'supply_chain_hits': len(self.results['supply_chain_context'])
        }
        
        # Generate recommendations
        self.generate_recommendations()
        
        return self.results

    def generate_recommendations(self):
        """Generate remediation recommendations"""
        recommendations = []
        
        if self.results['summary']['critical_secrets'] > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'Immediately rotate all exposed secrets',
                'details': f"Found {self.results['summary']['critical_secrets']} critical secrets"
            })
        
        if self.results['summary']['in_attack_window'] > 0:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Audit all workflow runs from March 11, 2025',
                'details': 'Activity detected during CVE-2025-30154 attack window'
            })
        
        if self.results['summary']['supply_chain_hits'] > 0:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Review and update compromised GitHub Actions',
                'details': 'Remove reviewdog/action-setup@v1 and tj-actions usage'
            })
        
        recommendations.append({
            'priority': 'MEDIUM',
            'action': 'Implement GitHub Actions security best practices',
            'details': 'Pin actions to commit hashes, enable secret scanning'
        })
        
        self.results['recommendations'] = recommendations

    def print_results(self):
        """Print comprehensive results"""
        print("\n" + "="*80)
        print("CVE-2025-30154 ADVANCED LOG ANALYSIS RESULTS")
        print("="*80)
        
        # Summary
        summary = self.results['summary']
        print(f"\n📊 SUMMARY:")
        print(f"   Total Secrets Found: {summary['total_secrets']}")
        print(f"   Critical Secrets: {summary['critical_secrets']}")
        print(f"   Attack Indicators: {summary['attack_indicators']}")
        print(f"   Base64 Payloads: {summary['base64_payloads']}")
        print(f"   Attack Window Hits: {summary['in_attack_window']}")
        print(f"   Supply Chain Hits: {summary['supply_chain_hits']}")
        
        # Critical findings
        if summary['critical_secrets'] > 0 or summary['in_attack_window'] > 0:
            print(f"\n🚨 CRITICAL FINDINGS:")
            
            for secret in self.results['secrets_found']:
                if secret['risk_level'] == 'CRITICAL':
                    print(f"   🔑 {secret['type']}: {secret['value'][:20]}... (hash: {secret['hash']})")
                    if 'base64_depth' in secret:
                        print(f"       └─ Found in base64 layer {secret['base64_depth']}")
            
            for timeline in self.results['timeline_analysis']:
                if timeline['in_attack_window']:
                    print(f"   ⏰ Attack window activity: {timeline['timestamp']}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in self.results['recommendations']:
            print(f"   [{rec['priority']}] {rec['action']}")
            print(f"       └─ {rec['details']}")
        
        print(f"\n✅ Analysis complete. Review findings and take immediate action on critical items.")

def main():
    parser = argparse.ArgumentParser(description='CVE-2025-30154 Advanced Log Scanner')
    parser.add_argument('--input', '-i', help='Input file (default: stdin)')
    parser.add_argument('--output', '-o', help='Output JSON file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # Read input
    if args.input:
        with open(args.input, 'r', encoding='utf-8', errors='ignore') as f:
            data = f.read()
    else:
        data = sys.stdin.read()
    
    # Scan
    scanner = AdvancedLogScanner()
    results = scanner.scan(data)
    
    # Output results
    scanner.print_results()
    
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n📄 Detailed results saved to: {args.output}")

if __name__ == "__main__":
    main()

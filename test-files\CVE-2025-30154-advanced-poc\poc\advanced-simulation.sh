#!/usr/bin/env bash
set -euo pipefail

# CVE-2025-30154 Advanced PoC - Real Attack Simulation
# This script demonstrates the actual attack vectors used in the supply chain compromise

echo "🚀 CVE-2025-30154 Advanced PoC - Supply Chain Attack Simulation"
echo "================================================================"

# Configuration
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
ATTACK_DATE="2025-03-11T19:15:42Z"  # Within the actual attack window
POC_DIR="$(dirname "$0")"
RESULTS_DIR="${POC_DIR}/../results"
mkdir -p "$RESULTS_DIR"

echo "📅 Simulating attack from: $ATTACK_DATE"
echo "📁 Results will be saved to: $RESULTS_DIR"

# Simulate real GitHub tokens (FAKE - for demonstration only)
generate_fake_tokens() {
    echo "🔑 Generating realistic fake tokens for demonstration..."
    
    # GitHub Personal Access Token (classic)
    FAKE_GHP="ghp_$(openssl rand -hex 18)"
    
    # GitHub App Installation Token
    FAKE_GHS="ghs_$(openssl rand -hex 18)"
    
    # GitHub OAuth Token
    FAKE_GHO="gho_$(openssl rand -hex 18)"
    
    # AWS Access Key
    FAKE_AWS="AKIA$(openssl rand -hex 8 | tr '[:lower:]' '[:upper:]')"
    
    # Slack Bot Token
    FAKE_SLACK="xoxb-$(openssl rand -dec 11)-$(openssl rand -dec 11)-$(openssl rand -base64 18 | tr -d '=+/')"
    
    echo "Generated fake tokens:"
    echo "  GitHub PAT: $FAKE_GHP"
    echo "  GitHub App: $FAKE_GHS"
    echo "  GitHub OAuth: $FAKE_GHO"
    echo "  AWS Key: $FAKE_AWS"
    echo "  Slack Bot: $FAKE_SLACK"
}

# Simulate the actual malicious payload injection
simulate_malicious_injection() {
    echo ""
    echo "💀 Simulating malicious payload injection (reviewdog/action-setup@v1)..."
    
    # Create fake environment variables that would be present
    export GITHUB_TOKEN="$FAKE_GHS"
    export AWS_ACCESS_KEY_ID="$FAKE_AWS"
    export SLACK_BOT_TOKEN="$FAKE_SLACK"
    export CUSTOM_SECRET="super_secret_api_key_$(openssl rand -hex 8)"
    
    # Simulate the actual malicious install.sh behavior
    echo "🐶 Preparing environment (simulating compromised install.sh)..."
    
    # Create a realistic memory dump simulation
    MEMORY_DUMP="GITHUB_TOKEN=$GITHUB_TOKEN
AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
SLACK_BOT_TOKEN=$SLACK_BOT_TOKEN
CUSTOM_SECRET=$CUSTOM_SECRET
PATH=/usr/local/bin:/usr/bin:/bin
HOME=/home/<USER>
RUNNER_OS=Linux
GITHUB_REPOSITORY=victim/repository
GITHUB_REF=refs/heads/main"
    
    # Double base64 encode (actual attack method)
    B64_ONCE=$(echo "$MEMORY_DUMP" | base64 -w0)
    B64_TWICE=$(echo "$B64_ONCE" | base64 -w0)
    
    echo ""
    echo "[poc] BEGIN ENCODED BLOCK"
    echo "$B64_TWICE"
    echo "[poc] END ENCODED BLOCK"
    
    # Save to results
    cat > "$RESULTS_DIR/malicious-payload.txt" << EOF
# CVE-2025-30154 Malicious Payload Simulation
# Timestamp: $TIMESTAMP
# Attack Window: $ATTACK_DATE

## Original Memory Dump:
$MEMORY_DUMP

## Single Base64 Encoding:
$B64_ONCE

## Double Base64 Encoding (Attack Payload):
$B64_TWICE
EOF
    
    echo ""
    echo "💾 Payload saved to: $RESULTS_DIR/malicious-payload.txt"
}

# Simulate supply chain propagation to tj-actions
simulate_supply_chain_propagation() {
    echo ""
    echo "🔗 Simulating supply chain propagation (reviewdog → tj-actions)..."
    
    # Simulate the PAT compromise that led to tj-actions compromise
    COMPROMISED_PAT="ghp_$(openssl rand -hex 18)"
    
    echo "📡 Simulated PAT compromise: $COMPROMISED_PAT"
    echo "🎯 Target: tj-actions/changed-files repository"
    
    # Create supply chain analysis
    cat > "$RESULTS_DIR/supply-chain-analysis.json" << EOF
{
  "attack_chain": {
    "initial_compromise": {
      "action": "reviewdog/action-setup@v1",
      "timestamp": "$ATTACK_DATE",
      "method": "malicious_code_injection"
    },
    "lateral_movement": {
      "target": "tj-actions-bot",
      "compromised_token": "$COMPROMISED_PAT",
      "method": "token_extraction_from_memory"
    },
    "secondary_compromise": {
      "action": "tj-actions/changed-files",
      "timestamp": "2025-03-15T10:30:00Z",
      "method": "repository_modification"
    }
  },
  "impact_analysis": {
    "affected_repositories": "10000+",
    "exposed_secrets": "high",
    "attack_duration": "72_hours",
    "detection_delay": "48_hours"
  }
}
EOF
    
    echo "📊 Supply chain analysis saved to: $RESULTS_DIR/supply-chain-analysis.json"
}

# Simulate historical compromise detection
simulate_historical_detection() {
    echo ""
    echo "🕵️ Simulating historical compromise detection..."
    
    # Create fake workflow run logs from the attack period
    cat > "$RESULTS_DIR/historical-workflow-logs.txt" << EOF
2025-03-11T18:45:23Z [INFO] Starting workflow run
2025-03-11T18:45:24Z [INFO] Checking out repository
2025-03-11T18:45:25Z [INFO] Setting up reviewdog/action-setup@v1
2025-03-11T18:45:26Z [DEBUG] 🐶 Preparing environment...
2025-03-11T18:45:27Z [DEBUG] Installing reviewdog binary
2025-03-11T18:45:28Z [DEBUG] $B64_TWICE
2025-03-11T18:45:29Z [INFO] reviewdog setup completed
2025-03-11T18:45:30Z [INFO] Running code analysis
2025-03-11T18:45:35Z [INFO] Workflow completed successfully
EOF
    
    echo "📜 Historical logs created: $RESULTS_DIR/historical-workflow-logs.txt"
}

# Generate comprehensive debug data
generate_debug_data() {
    echo ""
    echo "🐛 Generating comprehensive debug data..."
    
    cat > "$RESULTS_DIR/debug-summary.md" << EOF
# CVE-2025-30154 PoC Debug Summary

## Execution Details
- **Timestamp**: $TIMESTAMP
- **Attack Simulation**: $ATTACK_DATE
- **PoC Version**: Advanced v2.0

## Detected Indicators
- ✅ reviewdog/action-setup@v1 usage
- ✅ Malicious payload injection
- ✅ Double base64 encoding
- ✅ Memory dump simulation
- ✅ Supply chain propagation
- ✅ Historical compromise artifacts

## Extracted Secrets (FAKE)
- GitHub PAT: $FAKE_GHP
- GitHub App Token: $FAKE_GHS
- AWS Access Key: $FAKE_AWS
- Custom Secret: $CUSTOM_SECRET

## Attack Chain Analysis
1. Initial compromise: reviewdog/action-setup@v1
2. Memory dump execution
3. Secret extraction via double-base64
4. PAT compromise: $COMPROMISED_PAT
5. Lateral movement to tj-actions
6. Secondary repository compromise

## Validation Commands
\`\`\`bash
# Decode the payload
echo "$B64_TWICE" | base64 -d | base64 -d

# Scan for indicators
grep -r "🐶 Preparing environment" .
grep -r "$B64_TWICE" .

# Analyze supply chain
cat $RESULTS_DIR/supply-chain-analysis.json
\`\`\`

## Remediation
1. Remove reviewdog/action-setup@v1 usage
2. Rotate all exposed secrets
3. Audit workflow run logs
4. Implement action pinning
5. Enable secret scanning
EOF
    
    echo "📋 Debug summary created: $RESULTS_DIR/debug-summary.md"
}

# Main execution
main() {
    generate_fake_tokens
    simulate_malicious_injection
    simulate_supply_chain_propagation
    simulate_historical_detection
    generate_debug_data
    
    echo ""
    echo "✅ CVE-2025-30154 Advanced PoC completed successfully!"
    echo ""
    echo "📁 Results available in: $RESULTS_DIR/"
    echo "   - malicious-payload.txt"
    echo "   - supply-chain-analysis.json"
    echo "   - historical-workflow-logs.txt"
    echo "   - debug-summary.md"
    echo ""
    echo "🔍 Use these files to validate the Nuclei templates"
    echo "🚀 This PoC demonstrates real attack vectors and provides rich debug data"
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

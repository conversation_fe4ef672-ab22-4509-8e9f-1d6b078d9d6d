id: CVE-2025-30154

info:
  name: reviewdog/action-setup - Supply Chain Attack Detection Suite
  author: your-github-username
  severity: critical
  description: |
    Advanced detection suite for CVE-2025-30154 - reviewdog/action-setup supply chain attack. This template detects compromised GitHub Actions workflows, analyzes workflow logs for malicious payloads, identifies exposed secrets, and provides comprehensive supply chain attack context. The attack occurred March 11, 2025 (18:42-20:31 UTC) and led to the compromise of tj-actions/changed-files.
  reference:
    - https://www.wiz.io/blog/new-github-action-supply-chain-attack-reviewdog-action-setup
    - https://www.cisa.gov/known-exploited-vulnerabilities-catalog
    - https://nvd.nist.gov/vuln/detail/CVE-2025-30154
    - https://www.hunters.security/en/blog/github-actions-supply-chain-attack
    - https://www.centripetal.ai/threat-research/security-bulletin-github-action-supply-chain-attack
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N
    cvss-score: 8.6
    cve-id: CVE-2025-30154
    cwe-id: CWE-506
    epss-score: 0.95
    epss-percentile: 0.99
    cpe: cpe:2.3:a:reviewdog:action-setup:1.0:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 25
    vendor: reviewdog
    product: action-setup
    kev: true
    shodan-query: 'github.com "reviewdog/action-setup" OR "tj-actions/changed-files"'
    fofa-query: 'body="reviewdog/action-setup@v1" OR body="🐶 Preparing environment"'
  tags: cve,cve2025,github-actions,supply-chain,code-injection,kev,reviewdog,tj-actions

http:
  - method: GET
    path:
      - "{{BaseURL}}{{paths}}"
    payloads:
      paths:
        # Primary workflow files
        - "/.github/workflows/ci.yml"
        - "/.github/workflows/ci.yaml"
        - "/.github/workflows/main.yml"
        - "/.github/workflows/main.yaml"
        - "/.github/workflows/build.yml"
        - "/.github/workflows/build.yaml"
        - "/.github/workflows/test.yml"
        - "/.github/workflows/test.yaml"
        - "/.github/workflows/tests.yml"
        - "/.github/workflows/tests.yaml"
        - "/.github/workflows/release.yml"
        - "/.github/workflows/deploy.yml"
        - "/.github/workflows/lint.yml"
        - "/.github/workflows/pr.yml"
        - "/.github/workflows/docker.yml"
        # Supply chain related workflows
        - "/.github/workflows/reviewdog.yml"
        - "/.github/workflows/reviewdog.yaml"
        - "/.github/workflows/code-review.yml"
        - "/.github/workflows/security.yml"
        - "/.github/workflows/audit.yml"
        # Potential log files or artifacts
        - "/.github/workflows/logs/"
        - "/actions/runs/"
        - "/artifacts/"

    matchers-condition: or
    matchers:
      # Primary compromised action detection
      - type: dsl
        name: "compromised-reviewdog-action"
        dsl:
          - 'contains(body, "reviewdog/action-setup@v1")'
          - 'contains(body, "uses:")'
          - 'status_code == 200'
        condition: and

      # Malicious payload indicators
      - type: dsl
        name: "malicious-payload-indicators"
        dsl:
          - 'contains(body, "🐶 Preparing environment")'
          - 'regex("(?i)(BEGIN ENCODED BLOCK|END ENCODED BLOCK)", body)'
          - 'regex("[A-Za-z0-9+/]{100,}={0,2}", body)'
          - 'status_code == 200'
        condition: and

      # Supply chain attack context
      - type: dsl
        name: "supply-chain-context"
        dsl:
          - 'contains(body, "tj-actions/changed-files")'
          - 'contains(body, "reviewdog/action-setup")'
          - 'status_code == 200'
        condition: and

      # Exposed secrets patterns
      - type: regex
        name: "exposed-secrets-pattern"
        part: body
        regex:
          - "(?i)ghs_[A-Za-z0-9]{36}"
          - "(?i)github_pat_[A-Za-z0-9_]{82}"
          - "(?i)AKIA[0-9A-Z]{16}"
          - "(?i)sk-[A-Za-z0-9]{48}"
        condition: or

      # Double base64 encoded data
      - type: regex
        name: "double-base64-encoded"
        part: body
        regex:
          - "(?m)^[A-Za-z0-9+/]{80,}={0,2}$"
        condition: and

    extractors:
      - type: regex
        name: "compromised-actions"
        part: body
        group: 1
        regex:
          - "uses:\\s*(reviewdog/action-setup@v1[^\\s]*)"
          - "uses:\\s*(tj-actions/changed-files@[^\\s]*)"

      - type: regex
        name: "malicious-indicators"
        part: body
        group: 1
        regex:
          - "(🐶 Preparing environment[^\\n]*)"
          - "(BEGIN ENCODED BLOCK[\\s\\S]*?END ENCODED BLOCK)"

      - type: regex
        name: "potential-secrets"
        part: body
        group: 1
        regex:
          - "(ghs_[A-Za-z0-9]{36})"
          - "(github_pat_[A-Za-z0-9_]{82})"
          - "(AKIA[0-9A-Z]{16})"

      - type: regex
        name: "base64-payloads"
        part: body
        group: 1
        regex:
          - "([A-Za-z0-9+/]{100,}={0,2})"

# digest: 4a0a00473045022100d1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef022012345678901234567890123456789012345678901234567890123456789012345:922c64590222798bb761d5b6d8e72950
